"use client";

import { useEffect, useRef, useState } from 'react';

// Type definition for the OpnForm window object
declare global {
  interface Window {
    initEmbed?: (formId: string) => void;
  }
}

const FreeEstimateForm = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    // Set a timeout to handle cases where the script never loads
    const timeoutId = setTimeout(() => {
      if (isLoading) {
        console.warn('OpnForm script loading timed out, falling back to direct iframe');
        setIsLoading(false);
        // Don't set hasError here, let the iframe load directly
      }
    }, 5000); // 5 second timeout

    // For now, skip the external script loading and use direct iframe
    // This avoids the script loading error while maintaining functionality
    setIsLoading(false);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [isLoading]);

  return (
    <div ref={containerRef}>
      {isLoading && (
        <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#4A6C6F] mx-auto mb-4"></div>
            <p className="text-gray-600">Loading contact form...</p>
          </div>
        </div>
      )}

      {hasError && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Get Your Free Estimate</h3>
          <p className="text-gray-700 mb-6">Contact us directly for your free estimate and consultation.</p>
          <div className="grid md:grid-cols-2 gap-4">
            <a
              href="tel:************"
              className="flex items-center justify-center gap-2 bg-[#4A6C6F] text-white px-6 py-3 rounded-lg hover:bg-[#3a5a5d] transition-colors"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              Call ************
            </a>
            <a
              href="mailto:<EMAIL>"
              className="flex items-center justify-center gap-2 bg-[#709CA7] text-white px-6 py-3 rounded-lg hover:bg-[#4A6C6F] transition-colors"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Send Email
            </a>
          </div>
        </div>
      )}

      {!hasError && (
        <iframe
          style={{
            border: 'none',
            width: '100%',
            height: '700px',
            display: isLoading ? 'none' : 'block'
          }}
          id="free-estimate-form-afcau9"
          src="https://opnform.com/forms/free-estimate-form-afcau9"
          title="Free Estimate Form"
          sandbox="allow-scripts allow-forms allow-same-origin allow-popups"
          loading="lazy"
          onError={() => {
            console.warn('OpnForm iframe failed to load');
            setHasError(true);
          }}
        />
      )}
    </div>
  );
};

export default FreeEstimateForm;