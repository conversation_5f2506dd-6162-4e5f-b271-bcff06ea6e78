{"name": "3s-builder-solution", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "cross-env ANALYZE=true npm run build"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-tabs": "1.1.2", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "8.5.1", "lucide-react": "^0.454.0", "next": "^15.5.2", "next-themes": "^0.4.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.2", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "cross-env": "^7.0.3", "eslint": "9.34.0", "eslint-config-next": "15.5.2", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}