import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"

interface ServiceCardProps {
  title: string
  description: string
  image: string
  link: string
  location?: string
  variant?: "card" | "simple"
  features?: string[]
}

export default function ServiceCard({ 
  title, 
  description, 
  image, 
  link, 
  location,
  variant = "simple",
  features 
}: ServiceCardProps) {
  const altText = location 
    ? `${title} services in ${location}` 
    : `Professional ${title.toLowerCase()} services by 3S Builder Solution in Southeastern Pennsylvania`

  if (variant === "card") {
    return (
      <Link href={link} className="group">
        <Card className="overflow-hidden hover:shadow-lg transition-shadow h-full">
          <div className="relative h-48">
            <Image
              src={image || "/placeholder.svg"}
              alt={altText}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
              className="object-cover"
            />
          </div>
          <CardContent className="p-6">
            <h3 className="text-xl font-bold text-gray-900 group-hover:text-[#4A6C6F] transition-colors mb-3">
              {title}
            </h3>
            <p className="text-gray-600 mb-4">{description}</p>
            {features && (
              <ul className="space-y-2 mb-4">
                {features.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-700">
                    <svg className="h-4 w-4 mr-2 text-[#4A6C6F]" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
            )}
            <span className="text-[#4A6C6F] hover:text-[#3a5a5d] font-medium">
              Learn More →
            </span>
          </CardContent>
        </Card>
      </Link>
    )
  }

  return (
    <Link href={link} className="group">
      <div className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-lg transition-shadow h-full">
        <div className="relative h-48">
          <Image
            src={image || "/placeholder.svg"}
            alt={altText}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
            className="object-cover"
          />
        </div>
        <div className="p-6">
          <h3 className="text-xl font-bold mb-2 group-hover:text-[#4A6C6F] transition-colors">
            {title}
          </h3>
          <p className="text-gray-700 mb-4">{description}</p>
          <span className="text-[#4A6C6F] hover:text-[#3a5a5d] font-medium">
            Learn more about our {title.toLowerCase()} services →
          </span>
        </div>
      </div>
    </Link>
  )
}
